<template>
  <div class="merchant-type-manager">
    <el-card :bordered="false" shadow="never" class="ivu-mt" :body-style="{ padding: 0 }">
      <div class="padding-add">
        <el-form ref="searchForm" :model="searchForm" inline label-width="80px" label-position="right" @submit.native.prevent>
          <el-form-item label="类型名称：" prop="type_name" label-for="type_name">
            <el-input
              clearable
              placeholder="请输入类型名称"
              v-model="searchForm.type_name"
              class="form_content_width"
            />
          </el-form-item>
          <el-form-item label="状态：" label-for="status">
            <el-select
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              class="form_content_width"
            >
              <el-option value="1" label="启用"></el-option>
              <el-option value="0" label="禁用"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <el-button v-auth="['admin-merchant-type-create']" type="primary" class="bnt" @click="handleAdd">添加商家类型</el-button>

      <vxe-table
        class="mt14"
        highlight-hover-row
        :loading="loading"
        header-row-class-name="false"
        :data="tableData"
      >
        <vxe-table-column field="id" title="编号" tooltip width="80"></vxe-table-column>
        <vxe-table-column field="type_name" title="类型名称" min-width="150"></vxe-table-column>
        <vxe-table-column field="type_require" title="类型要求" min-width="150"></vxe-table-column>
        <vxe-table-column field="merchant_count" title="店铺数量" min-width="150"></vxe-table-column>
        <vxe-table-column field="status_txt" title="状态" min-width="100">
          <template v-slot="{ row }">
            <el-switch
              class="defineSwitch"
              :active-value="1"
              :inactive-value="0"
              v-model="row.status"
              @change="handleStatusChange(row)"
              size="large"
              active-text="启用"
              inactive-text="禁用"
            >
            </el-switch>
          </template>
        </vxe-table-column>
        <vxe-table-column field="add_time" title="添加时间" min-width="160"></vxe-table-column>
        <vxe-table-column field="update_time" title="最后更新时间" min-width="160"></vxe-table-column>
        <vxe-table-column field="operation" title="操作" width="150" fixed="right">
          <template v-slot="{ row, index }">
            <a @click="handleEdit(row)">编辑</a>
            <el-divider direction="vertical"></el-divider>
            <a @click="handleDelete(row, index)">删除</a>
          </template>
        </vxe-table-column>
      </vxe-table>

      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.limit"
          @pagination="handlePageChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import {
  merchantTypeListApi,
  merchantTypeCreateApi,
  merchantTypeEditApi,
  merchantTypeDeleteApi,
  merchantTypeStatusApi
} from '@/api/merchant';

export default {
  name: 'merchant_type',
  data() {
    return {
      loading: false,
      searchForm: {
        type_name: '',
        status: '',
        page: 1,
        limit: 15,
      },
      total: 0,
      tableData: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    // 获取列表数据
    getList() {
      this.loading = true;

      
      // 生产环境调用真实API
      merchantTypeListApi(this.searchForm)
        .then((res) => {
          this.loading = false;
          this.tableData = res.data.list || [];
          this.total = res.data.count || 0;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg || '获取数据失败');
        });
    },

    // 搜索
    handleSearch() {
      this.searchForm.page = 1;
      this.getList();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        type_name: '',
        status: '',
        page: 1,
        limit: 15,
      };
      this.getList();
    },

    // 分页变化
    handlePageChange() {
      this.getList();
    },

    // 添加商家类型
    handleAdd() {

      this.$modalForm(merchantTypeCreateApi()).then(() => this.getList());
    },

    // 编辑商家类型
    handleEdit(row) {

      this.$modalForm(merchantTypeEditApi(row.id)).then(() => this.getList());
    },

    // 状态切换
    handleStatusChange(row) {

      const data = {
        id: row.id,
        status: row.status,
      };
      merchantTypeStatusApi(data)
        .then((res) => {
          this.$message.success(res.msg || '状态修改成功');
        })
        .catch((res) => {
          this.$message.error(res.msg || '状态修改失败');
          // 恢复原状态
          row.status = row.status === 1 ? 0 : 1;
        });
    },

    // 删除商家类型
    handleDelete(row, index) {
      const deleteData = {
        title: '删除商家类型',
        num: index,
        url: `merchant/type/${row.id}`,
        method: 'DELETE',
        ids: '',
      };
      this.$modalSure(deleteData)
        .then((res) => {
          this.$message.success(res.msg || '删除成功');
          this.getList();
        })
        .catch((res) => {
          this.$message.error(res.msg || '删除失败');
        });
    },
  },
};
</script>

<style scoped lang="stylus">
.merchant-type-manager {
  .padding-add {
    padding: 20px;
  }

  .form_content_width {
    width: 200px;
  }

  .bnt {
    margin-bottom: 14px;
  }

  .page {
    margin-top: 20px;
  }
}
</style>