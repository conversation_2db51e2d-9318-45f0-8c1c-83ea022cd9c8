<template>
  <div>
    <!-- 搜索区域 -->
    <el-card :bordered="false" shadow="never" :body-style="{padding:0}">
      <div class="padding-add">
        <el-form
          ref="searchForm"
          :model="searchForm"
          :label-width="labelWidth"
          :label-position="labelPosition"
          @submit.native.prevent
          inline
        >
          <el-form-item label="店铺名称：">
            <el-input
              clearable
              placeholder="请输入店铺名称"
              v-model="searchForm.store_name"
              class="form_content_width"
            />
          </el-form-item>
          <el-form-item label="联系人：">
            <el-input
              clearable
              placeholder="请输入联系人姓名"
              v-model="searchForm.name"
              class="form_content_width"
            />
          </el-form-item>
          <el-form-item label="联系电话：">
            <el-input
              clearable
              placeholder="请输入联系电话"
              v-model="searchForm.phone"
              class="form_content_width"
            />
          </el-form-item>
          <el-form-item label="申请状态：">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable class="form_content_width">
              <el-option label="待审核" :value="0"></el-option>
              <el-option label="已通过" :value="1"></el-option>
              <el-option label="已拒绝" :value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 列表区域 -->
    <el-card :bordered="false" shadow="never" class="ivu-mt mt16">
      <el-table
        :data="tableData"
        ref="table"
        class="mt14"
        v-loading="loading"
        highlight-current-row
        empty-text="暂无数据"
      >
        <el-table-column label="ID" width="80" prop="id" />

        <el-table-column label="店铺名称" min-width="120" prop="store_name" />

        <el-table-column label="店铺分类" min-width="100" prop="class_name" />

        <el-table-column label="店铺类型" min-width="100" prop="type_name" />

        <el-table-column label="联系人" min-width="100" prop="name" />

        <el-table-column label="联系电话" min-width="120" prop="phone" />

        <el-table-column label="Logo" min-width="80">
          <template slot-scope="scope">
            <div v-if="scope.row.door_header_img" class="tabBox_img" v-viewer>
              <img v-lazy="scope.row.door_header_img" />
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="资质" min-width="100">
          <template slot-scope="scope">
            <div v-if="scope.row.zizhi_imgs && scope.row.zizhi_imgs.length > 0" class="frame-images">
              <div class="tabBox_img" v-viewer v-for="(img, index) in scope.row.zizhi_imgs" :key="index">
                <img v-lazy="img" />
              </div>
            </div>
            <span v-else>-</span>
          </template>
        </el-table-column>

        <el-table-column label="申请时间" min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.add_time }}</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" min-width="80">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="审核时间" min-width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.update_time || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="备注" min-width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.remarks || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="审核人" min-width="100">
          <template slot-scope="scope">
            <span>{{ scope.row.admin_name || '-' }}</span>
          </template>
        </el-table-column>

        <el-table-column label="操作" min-width="120" fixed="right">
          <template slot-scope="scope">
            <el-button
              v-if="scope.row.status === 0"
              type="text"
              size="small"
              @click="handleAudit(scope.row)"
            >
              审核
            </el-button>
            <el-button
              type="text"
              size="small"
              @click="handleDelete(scope.row)"
              style="color: #f56c6c;"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="acea-row row-right page">
        <pagination
          v-if="total"
          :total="total"
          :page.sync="searchForm.page"
          :limit.sync="searchForm.limit"
          @pagination="handlePageChange"
        />
      </div>
    </el-card>

    <!-- 审核对话框 -->
    <el-dialog title="审核申请" :visible.sync="auditDialogVisible" width="500px">
      <el-form ref="auditForm" :model="auditForm" :rules="auditRules" label-width="80px">
        <el-form-item label="审核结果" prop="status">
          <el-radio-group v-model="auditForm.status">
            <el-radio :label="1">通过</el-radio>
            <el-radio :label="2">拒绝</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remarks">
          <el-input
            type="textarea"
            :rows="4"
            placeholder="请输入审核备注"
            v-model="auditForm.remarks"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="auditDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitAudit" :loading="auditLoading">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { merchantApplyListApi, merchantApplyAuditApi, merchantApplyDeleteApi } from '@/api/merchant';
import pagination from '@/components/Pagination';

export default {
  name: 'MerchantApply',
  components: {
    pagination,
  },
  data() {
    return {
      labelWidth: 80,
      labelPosition: 'right',
      loading: false,
      auditLoading: false,
      tableData: [],
      total: 0,

      // 搜索表单
      searchForm: {
        store_name: '',
        name: '',
        phone: '',
        status: '',
        page: 1,
        limit: 15,
      },

      // 审核对话框
      auditDialogVisible: false,
      auditForm: {
        id: null,
        status: 1,
        remarks: '',
      },
      auditRules: {
        status: [
          { required: true, message: '请选择审核结果', trigger: 'change' }
        ],
      },
    };
  },

  mounted() {
    this.getList();
  },

  methods: {
    // 获取列表数据
    getList() {
      this.loading = true;
      merchantApplyListApi(this.searchForm)
        .then((res) => {
          this.loading = false;
          this.tableData = res.data.list || [];
          this.total = res.data.count || 0;
        })
        .catch((res) => {
          this.loading = false;
          this.$message.error(res.msg || '获取数据失败');
        });
    },

    // 搜索
    handleSearch() {
      this.searchForm.page = 1;
      this.getList();
    },

    // 重置搜索
    handleReset() {
      this.searchForm = {
        store_name: '',
        name: '',
        phone: '',
        status: '',
        page: 1,
        limit: 15,
      };
      this.getList();
    },

    // 分页变化
    handlePageChange() {
      this.getList();
    },

    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        0: '待审核',
        1: '已通过',
        2: '已拒绝',
      };
      return statusMap[status] || '未知';
    },

    // 获取状态标签类型
    getStatusType(status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'danger',
      };
      return typeMap[status] || '';
    },

    // 审核申请
    handleAudit(row) {
      this.auditForm = {
        id: row.id,
        status: 1,
        remarks: '',
      };
      this.auditDialogVisible = true;
    },

    // 提交审核
    submitAudit() {
      this.$refs.auditForm.validate((valid) => {
        if (valid) {
          this.auditLoading = true;
          merchantApplyAuditApi(this.auditForm)
            .then((res) => {
              this.auditLoading = false;
              this.auditDialogVisible = false;
              this.$message.success(res.msg || '审核成功');
              this.getList();
            })
            .catch((res) => {
              this.auditLoading = false;
              this.$message.error(res.msg || '审核失败');
            });
        }
      });
    },

    // 删除申请
    handleDelete(row) {
      this.$confirm('确定要删除这条申请记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          merchantApplyDeleteApi(row.id)
            .then((res) => {
              this.$message.success(res.msg || '删除成功');
              this.getList();
            })
            .catch((res) => {
              this.$message.error(res.msg || '删除失败');
            });
        })
        .catch(() => {
          // 用户取消删除
        });
    },
  },
};
</script>

<style scoped lang="stylus">
.padding-add
  padding 20px

.form_content_width
  width 200px

.mt16
  margin-top 16px

.tabBox_img
  width 36px
  height 36px
  border-radius 4px
  cursor pointer
  img
    width 100%
    height 100%
    object-fit cover

.frame-images
  display flex
  flex-wrap wrap
  gap 4px
  .tabBox_img
    width 30px
    height 30px

.page
  margin-top 20px
  padding 20px

.acea-row
  display flex
  &.row-right
    justify-content flex-end

.dialog-footer
  text-align right
</style>